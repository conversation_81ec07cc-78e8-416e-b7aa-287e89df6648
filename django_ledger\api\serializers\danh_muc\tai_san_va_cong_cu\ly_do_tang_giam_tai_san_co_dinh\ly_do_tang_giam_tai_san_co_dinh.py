"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for LyDoTangGiamTaiSanCoDinh model.
"""

from django.utils.translation import gettext_lazy as _  # noqa: F401
from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.entity import (  # noqa: F401
    EntityModelSerializer,
)
from django_ledger.models.danh_muc.tai_san_va_cong_cu.ly_do_tang_giam_tai_san_co_dinh import (  # noqa: F401,
    LyDoTangGiamTaiSanCoDinhModel,
)


class LyDoTangGiamTaiSanCoDinhSerializer(serializers.ModelSerializer):
    """
    Serializer for LyDoTangGiamTaiSanCoDinhModel.
    """

    class Meta:
        model = LyDoTangGiamTaiSanCoDinhModel
        fields = [
            'uuid',
            'entity_model',
            'loai_tg_ts',
            'ma_tg_ts',
            'ten_tg_ts',
            'ten_tg_ts2',
            'status',
            'created_by',
            'updated_by',
            'created',
            'updated',
        ]
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated']

    def validate_ma_tg_ts(self, value):  # noqa: C901
        """
        Validates ma_tg_ts is unique within the entity.

        Parameters
        ----------
        value : str
            The ma_tg_ts value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If ma_tg_ts already exists for this entity
        """
        request = self.context.get('request')
        if not request:
            return value

        # Get entity_slug from request context
        entity_slug = request.parser_context.get('kwargs', {}).get('entity_slug')
        if not entity_slug:
            return value

        # Get current instance if this is an update operation
        instance = getattr(self, 'instance', None)

        # Check if ma_tg_ts exists for this entity
        qs = LyDoTangGiamTaiSanCoDinhModel.objects.filter(
            entity_model__slug__exact=entity_slug,
            ma_tg_ts=value
        )

        # Exclude current instance if this is an update
        if instance:
            qs = qs.exclude(uuid=instance.uuid)

        if qs.exists():
            raise serializers.ValidationError(
                _("Mã tài sản tham gia đã tồn tại")
            )

        return value
